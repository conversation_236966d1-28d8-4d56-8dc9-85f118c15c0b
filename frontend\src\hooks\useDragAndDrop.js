import { useState, useEffect } from 'react';
import { arrayMove } from '@dnd-kit/sortable';
import { cloneDeep } from 'lodash';
import TaskService from '../services/taskService';
import TaskListService from '../services/taskListService';
import { useSocket } from '../contexts/SocketContext';

// Định nghĩa các loại đối tượng có thể kéo thả
const ACTIVE_DRAG_TYPE = {
    TASK_LIST: 'taskList', // Danh sách công việc
    TASK: 'task'           // Công việc đơn lẻ
};

/**
 * Hook xử lý logic kéo thả cho tasks và task lists
 * @param {Array} taskLists - Danh sách các task list hiện tại
 * @param {Function} setTaskLists - Function để cập nhật task lists
 * @param {Object} selectedBoard - Board đang được chọn
 * @param {Function} refreshBoards - Function để refresh boards
 */
const useDragAndDrop = (taskLists, setTaskLists, selectedBoard, refreshBoards) => {
    // State lưu trữ thông tin về đối tượng đang được kéo
    const [activeDragType, setActiveDragType] = useState(null); // Loại đối tượng (task/taskList)
    const [activeDragId, setActiveDragId] = useState(null);     // ID của đối tượng đang kéo
    const [activeDragData, setActiveDragData] = useState(null); // Dữ liệu của đối tượng đang kéo
    const [isAnyModalOpen, setIsAnyModalOpen] = useState(false); // Trạng thái modal (không cho phép kéo khi modal mở)

    const { socket, connected, emitTaskMoved, emitTaskListMoved } = useSocket();

    // Lắng nghe sự kiện task được di chuyển từ socket
    useEffect(() => {
        if (!socket || !connected) return;

        socket.on("task:moved", (data) => { 
            console.log("Socket task:moved received:", data);
            
            // Chỉ xử lý nếu event thuộc về board hiện tại
            if (data.boardId === selectedBoard) {
                const { task } = data;
                
                setTaskLists((prevTaskLists) => {
                    const newTaskLists = cloneDeep(prevTaskLists);
                    
                    // Xóa task khỏi task lists trước
                    newTaskLists.forEach(list => {
                        list.tasks = list.tasks.filter(t => t.id !== task.id);
                    });
                    
                    // Thêm task vào task list mới
                    const targetTaskList = newTaskLists.find(list => 
                        list.id === task.tasklistId
                    );
                    
                    if (targetTaskList) {
                        targetTaskList.tasks.push(task);
                        // Sắp xếp lại tasks theo position
                        targetTaskList.tasks.sort((a, b) => {
                            const posA = parseInt(a.position || '0', 10);
                            const posB = parseInt(b.position || '0', 10);
                            return posA - posB;
                        });
                    }
                    
                    return newTaskLists;
                });
            }
        });

        // Lắng nghe sự kiện task list được di chuyển
        socket.on("taskList:moved", (data) => {
            //console.log("Socket taskList:moved received:", data);
            
            // Chỉ xử lý nếu event thuộc về board hiện tại
            if (data.boardId === selectedBoard) {
                const { taskList } = data;
                
                setTaskLists((prevTaskLists) => {
                    const newTaskLists = cloneDeep(prevTaskLists);
                    
                    // Cập nhật thông tin task list
                    const index = newTaskLists.findIndex(list => list.id === taskList.id);
                    if (index !== -1) {
                        newTaskLists[index] = { ...newTaskLists[index], ...taskList };
                    }
                    
                    // Sắp xếp lại task lists theo position
                    newTaskLists.sort((a, b) => {
                        const posA = parseInt(a.position || '0', 10);
                        const posB = parseInt(b.position || '0', 10);
                        return posA - posB;
                    });
                    
                    return newTaskLists;
                });
            }
        });

        return () => {
            socket.off("task:moved");
            socket.off("taskList:moved");
        };
    }, [socket, connected, selectedBoard, setTaskLists]);

    /**
     * Tìm task list chứa task có ID cụ thể
     * @param {string} taskId - ID của task cần tìm
     * @returns {Object|null} Task list chứa task hoặc null nếu không tìm thấy
     */
    const findTaskListByTaskId = (taskId) => {
        return taskLists.find((taskList) =>
            taskList.tasks.map((task) => task.id).includes(taskId)
        );
    };

    /**
     * Reset trạng thái kéo thả về ban đầu
     */
    const resetDragState = () => {
        setActiveDragId(null);
        setActiveDragType(null);
        setActiveDragData(null);
    };

    /**
     * Xử lý sự kiện bắt đầu kéo
     * @param {Object} event - Event object từ dnd-kit
     */
    const onDragStart = (event) => {
        // Không cho phép kéo khi có modal đang mở
        if (isAnyModalOpen) {
            return;
        }

        // console.log('Drag start:', event);
        setActiveDragId(event?.active?.id);
        setActiveDragType(event?.active?.data?.current?.type);
        setActiveDragData(
            event?.active?.data?.current?.taskList ||
            event?.active?.data?.current?.task
        );
    };

    /**
     * Xử lý sự kiện kéo qua (drag over)
     * Hiện tại chỉ xử lý cho task, không xử lý cho task list
     * @param {Object} event - Event object từ dnd-kit
     */
    const onDragOver = (event) => {
        // Chỉ xử lý kéo thả task, không xử lý task list
        if (activeDragType === ACTIVE_DRAG_TYPE.TASK_LIST) {
            return;
        }

        const { active, over } = event;
        if (!over || !active) return;

        const {
            id: activeDraggingTaskId,
            data: {
                current: { task: activeDraggingTaskData }
            }
        } = active;
        const { id: overTaskId } = over;

        // Tìm task list chứa task đang kéo và task đang được kéo qua
        const activeTaskList = findTaskListByTaskId(activeDraggingTaskId);
        const overTaskList = findTaskListByTaskId(overTaskId);

        if (!activeTaskList || !overTaskList) return;
    };

    /**
     * Xử lý sự kiện kết thúc kéo thả
     * Đây là hàm chính xử lý logic di chuyển task/task list
     * @param {Object} event - Event object từ dnd-kit
     */
    const onDragEnd = async (event) => {
        const { active, over } = event;
        if (!over || !active) {
            resetDragState();
            return;
        }

        // Xử lý kéo thả task
        if (activeDragType === ACTIVE_DRAG_TYPE.TASK) {
            const {
                id: activeDraggingTaskId,
                data: {
                    current: { task: activeDraggingTaskData }
                }
            } = active;
            const { id: overTaskId } = over;

            // Tìm task list chứa task đang kéo
            const activeTaskList = findTaskListByTaskId(activeDraggingTaskId);
            let overTaskList;

            // Xác định task list đích (có thể là task list hoặc task)
            if (over.data.current?.type === ACTIVE_DRAG_TYPE.TASK_LIST) {
                overTaskList = taskLists.find((list) => list.id === overTaskId);
            } else {
                overTaskList = findTaskListByTaskId(overTaskId);
            }

            if (!activeTaskList || !overTaskList) {
                resetDragState();
                return;
            }

            let previousTaskId = null; // ID của task trước đó (để xác định vị trí)
            let nextTaskId = null;     // ID của task tiếp theo (để xác định vị trí)
            let parentTaskId = null;   // ID của task cha (nếu thả vào task cha)

            // Xác định vị trí thả task
            if (over.data.current?.type === ACTIVE_DRAG_TYPE.TASK_LIST) {
                // Thả vào task list (thêm vào cuối)
                if (overTaskList.tasks.length > 0) {
                    previousTaskId = overTaskList.tasks[overTaskList.tasks.length - 1].id;
                }
            } else {
                // Thả vào task cụ thể
                const overTaskIndex = overTaskList.tasks.findIndex(
                    (task) => task.id === overTaskId
                );

                // Kiểm tra xem task có được thả bên dưới task đích không
                const isBelowOverItem =
                    active.rect.current.translate &&
                    active.rect.current.translate.top >
                    over.rect.top + over.rect.height;

                if (isBelowOverItem) {
                    // Thả bên dưới task đích
                    previousTaskId = overTaskId;
                    // Tìm task tiếp theo
                    if (overTaskIndex < overTaskList.tasks.length - 1) {
                        nextTaskId = overTaskList.tasks[overTaskIndex + 1].id;
                    }
                    const isDroppedAsChild = false; // Có thể mở rộng để hỗ trợ thả làm task con

                    if (isDroppedAsChild) {
                        parentTaskId = overTaskId;
                        previousTaskId = null;
                        nextTaskId = null;
                    }
                } else {
                    // Thả bên trên task đích
                    nextTaskId = overTaskId;
                    // Tìm task trước đó
                    if (overTaskIndex > 0) {
                        previousTaskId = overTaskList.tasks[overTaskIndex - 1].id;
                    }
                }
            }

            // Cập nhật UI ngay lập tức (optimistic update)
            setTaskLists((prevTaskLists) => {
                const newTaskLists = cloneDeep(prevTaskLists);

                // Trường hợp 1: Di chuyển trong cùng một task list
                if (activeTaskList.id === overTaskList.id) {
                    const currentTaskList = newTaskLists.find(
                        (list) => list.id === activeTaskList.id
                    );

                    if (currentTaskList) {
                        const oldIndex = currentTaskList.tasks.findIndex(
                            (task) => task.id === activeDraggingTaskId
                        );
                        const newIndex = currentTaskList.tasks.findIndex(
                            (task) => task.id === overTaskId
                        );

                        if (oldIndex !== -1 && newIndex !== -1) {
                            // Sử dụng arrayMove để sắp xếp lại tasks
                            currentTaskList.tasks = arrayMove(
                                currentTaskList.tasks,
                                oldIndex,
                                newIndex
                            );
                        }
                    }
                } else {
                    // Trường hợp 2: Di chuyển giữa các task list khác nhau
                    const nextActiveTaskList = newTaskLists.find(
                        (taskList) => taskList.id === activeTaskList.id
                    );
                    const nextOverTaskList = newTaskLists.find(
                        (taskList) => taskList.id === overTaskList.id
                    );

                    if (nextActiveTaskList && nextOverTaskList) {
                        // Kiểm tra xem task đang kéo có phải là task cha không
                        const isDraggingParentTask =
                            activeDraggingTaskData.isParent ||
                            (activeDraggingTaskData.children &&
                                activeDraggingTaskData.children.length > 0);

                        // Xóa task khỏi task list nguồn
                        const updatedActiveTasks = nextActiveTaskList.tasks.filter((task) => {
                            if (task.id === activeDraggingTaskId) {
                                return false; // Xóa task chính
                            }
                            if (isDraggingParentTask && task.parentId === activeDraggingTaskId) {
                                return false; // Xóa các task con nếu kéo task cha
                            }
                            return true;
                        });
                        nextActiveTaskList.tasks = updatedActiveTasks;

                        // Cập nhật thông tin task được di chuyển
                        const updatedDraggingTask = {
                            ...activeDraggingTaskData,
                            listId: nextOverTaskList.id,
                            parentId: parentTaskId || null
                        };

                        // Nếu không phải task cha và có parent, thì xóa parent
                        if (!isDraggingParentTask && activeDraggingTaskData.parentId) {
                            updatedDraggingTask.parentId = null;
                        }

                        // Xác định vị trí chèn task
                        let insertIndex;
                        if (over.data.current?.type === ACTIVE_DRAG_TYPE.TASK_LIST) {
                            // Thả vào task list (chèn vào cuối)
                            insertIndex = nextOverTaskList.tasks.length;
                        } else {
                            // Thả vào task cụ thể
                            const overTaskIndex = nextOverTaskList.tasks.findIndex(
                                (task) => task.id === overTaskId
                            );
                            const isBelowOverItem =
                                active.rect.current.translate &&
                                active.rect.current.translate.top >
                                over.rect.top + over.rect.height;
                            const modifier = isBelowOverItem ? 1 : 0;
                            insertIndex = overTaskIndex >= 0
                                ? overTaskIndex + modifier
                                : nextOverTaskList.tasks.length;
                        }

                        // Xử lý danh sách task đích
                        let updatedOverTasks = [...nextOverTaskList.tasks];
                        
                        // Nếu kéo task cha, xóa các task con khỏi danh sách đích
                        if (isDraggingParentTask && activeDraggingTaskData.children?.length > 0) {
                            const childrenIds = activeDraggingTaskData.children.map(
                                (child) => child.id
                            );
                            updatedOverTasks = updatedOverTasks.filter(
                                (task) => !childrenIds.includes(task.id)
                            );
                        }

                        // Tính toán lại vị trí chèn
                        if (over.data.current?.type !== ACTIVE_DRAG_TYPE.TASK_LIST) {
                            const overTaskIndex = updatedOverTasks.findIndex(
                                (task) => task.id === overTaskId
                            );
                            const isBelowOverItem =
                                active.rect.current.translate &&
                                active.rect.current.translate.top >
                                over.rect.top + over.rect.height;
                            const modifier = isBelowOverItem ? 1 : 0;
                            insertIndex = overTaskIndex >= 0
                                ? overTaskIndex + modifier
                                : updatedOverTasks.length;
                        } else {
                            insertIndex = updatedOverTasks.length;
                        }

                        // Chèn task vào vị trí mới
                        updatedOverTasks.splice(insertIndex, 0, updatedDraggingTask);

                        // Nếu kéo task cha, chèn các task con theo sau
                        if (isDraggingParentTask && activeDraggingTaskData.children?.length > 0) {
                            activeDraggingTaskData.children.forEach((childTask, index) => {
                                const updatedChildTask = {
                                    ...childTask,
                                    listId: nextOverTaskList.id,
                                    parentId: activeDraggingTaskId
                                };
                                updatedOverTasks.splice(
                                    insertIndex + 1 + index,
                                    0,
                                    updatedChildTask
                                );
                            });
                        }

                        nextOverTaskList.tasks = updatedOverTasks;
                    }
                }

                return newTaskLists;
            });

            // Gọi API để cập nhật database
            const movedTask = await TaskService.moveTask(
                activeTaskList.id,
                activeDraggingTaskId,
                overTaskList.id,
                previousTaskId,
                nextTaskId,
                parentTaskId,
                selectedBoard
            );

            // Emit socket event để thông báo cho các client khác
            if (movedTask) {
                emitTaskMoved({
                    boardId: selectedBoard,
                    task: movedTask
                });
            }

            // Refresh boards để cập nhật trạng thái đồng bộ (backup)
            if (refreshBoards) {
                await refreshBoards();
            }
        } else if (activeDragType === ACTIVE_DRAG_TYPE.TASK_LIST) {
            // Xử lý kéo thả task list
            const { id: activeTaskListId } = active;
            const { id: overTaskListId } = over;

            // Không làm gì nếu thả vào chính nó
            if (activeTaskListId === overTaskListId) {
                resetDragState();
                return;
            }

            // Xác định previous và next task list dựa trên vị trí kéo thả
            const oldIndex = taskLists.findIndex(list => list.id === activeTaskListId);
            const overIndex = taskLists.findIndex(list => list.id === overTaskListId);
            
            let previousTaskListId = null;
            let nextTaskListId = null;

            // Xác định vị trí thả dựa trên vị trí chuột
            const isBelowOverItem = active.rect.current.translate &&
                active.rect.current.translate.top > over.rect.top + over.rect.height;

            if (isBelowOverItem) {
                // Thả bên dưới task list đích
                previousTaskListId = overTaskListId;
                // Tìm task list tiếp theo
                if (overIndex < taskLists.length - 1) {
                    nextTaskListId = taskLists[overIndex + 1].id;
                }
            } else {
                // Thả bên trên task list đích
                nextTaskListId = overTaskListId;
                // Tìm task list trước đó
                if (overIndex > 0) {
                    previousTaskListId = taskLists[overIndex - 1].id;
                }
            }

            // Cập nhật thứ tự task lists
            setTaskLists((prevTaskLists) => {
                const newTaskLists = cloneDeep(prevTaskLists);
                const oldIndex = newTaskLists.findIndex(
                    (list) => list.id === activeTaskListId
                );
                const newIndex = newTaskLists.findIndex(
                    (list) => list.id === overTaskListId
                );

                if (oldIndex !== -1 && newIndex !== -1) {
                    return arrayMove(newTaskLists, oldIndex, newIndex);
                }

                return newTaskLists;
            });

            //Gọi api để cập nhật
            const movedTaskList = await TaskListService.moveTaskList(
                activeTaskListId,
                previousTaskListId,
                nextTaskListId,
                selectedBoard
            );

            // Emit socket event để thông báo cho các client khác
            if (movedTaskList) {
                emitTaskListMoved({
                    boardId: selectedBoard,
                    taskList: movedTaskList
                });
            }

            // Refresh boards để cập nhật trạng thái đồng bộ (backup)
            if (refreshBoards) {
                await refreshBoards();
            }
        }   

        // Reset trạng thái kéo thả
        resetDragState();
    };

    return {
        activeDragType,
        activeDragId,
        activeDragData,
        isAnyModalOpen,
        setIsAnyModalOpen,
        onDragStart,
        onDragOver,
        onDragEnd,
        findTaskListByTaskId
    };
};

export default useDragAndDrop; 