import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
    Layout,
    Card,
    Typography,
    Spin,
    Alert,
    Tag,
    Avatar,
    Space,
    Divider,
    Empty,
    Button,
    Row,
    Col,
} from "antd";
import {
    UserOutlined,
    TeamOutlined,
    FolderOutlined,
    EyeOutlined,
    ArrowLeftOutlined,
    DoubleRightOutlined,
} from "@ant-design/icons";
import workspaceService from "../services/workspaceService";
import ReadOnlyTaskList from "../components/ReadOnlyTaskList";
import TaskDetailModal from "../components/TaskDetailModal";
import { getToken } from "../utils/tokenManager";
import axios from "../utils/axiosCustomize";
import "./PublicWorkspace.css";

const { Header, Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const PublicWorkspace = () => {
    const { publicShareId } = useParams();
    const navigate = useNavigate();
    const [workspace, setWorkspace] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedBoard, setSelectedBoard] = useState(null);
    const [taskLists, setTaskLists] = useState([]);
    const [taskListsLoading, setTaskListsLoading] = useState(false);
    const [taskListsError, setTaskListsError] = useState(null);
    const [viewMode, setViewMode] = useState("workspace"); // 'workspace' or 'board'
    const [isLoading, setIsLoading] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const [taskModalVisible, setTaskModalVisible] = useState(false);

    useEffect(() => {
        const fetchPublicWorkspace = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await workspaceService.getPublicWorkspace(
                    publicShareId
                );
                setWorkspace(data);
            } catch (err) {
                console.error("Error fetching public workspace:", err);
                setError(
                    err.response?.data?.message || "Không thể tải workspace"
                );
            } finally {
                setLoading(false);
            }
        };

        if (publicShareId) {
            fetchPublicWorkspace();
        }
    }, [publicShareId]);

    const fetchBoardTaskLists = async (boardId) => {
        try {
            setTaskListsLoading(true);
            setTaskListsError(null);
            const data = await workspaceService.getPublicBoardTaskLists(
                publicShareId,
                boardId
            );

            // Transform data to match expected format
            const transformedTaskLists = data.map((taskList) => ({
                ...taskList,
                name: taskList.title,
                tasks: taskList.tasks || [],
            }));

            setTaskLists(transformedTaskLists);
        } catch (err) {
            console.error("Error fetching board tasklists:", err);
            setTaskListsError(
                err.response?.data?.message || "Không thể tải danh sách task"
            );
        } finally {
            setTaskListsLoading(false);
        }
    };

    const handleBoardSelect = (board) => {
        setSelectedBoard(board);
        setViewMode("board");
        fetchBoardTaskLists(board.id);
    };

    const handleBackToWorkspace = () => {
        setViewMode("workspace");
        setSelectedBoard(null);
        setTaskLists([]);
    };

    const handleLogin = async () => {
        try {
            setIsLoading(true);

            // Check current token before login
            const existingToken = getToken();
            if (existingToken) {
                try {
                    const response = await axios.get("/auth/profile");
                    if (response.data.status === "success") {
                        // Token is valid, redirect directly to dashboard
                        navigate("/app/dashboard");
                        return;
                    }
                } catch (error) {
                    // Token is invalid, continue with Google login
                }
            }

            // Redirect to Google OAuth if no token or token is invalid
            window.location.href = `${
                import.meta.env.VITE_API_URL
            }/auth/google`;
        } catch (error) {
            console.error("Login error:", error);
            setIsLoading(false);
        }
    };

    const handleTaskDoubleClick = (task) => {
        setSelectedTask(task);
        setTaskModalVisible(true);
    };

    const handleCloseTaskModal = () => {
        setTaskModalVisible(false);
        setSelectedTask(null);
    };

    if (loading) {
        return (
            <Layout className="public-workspace-layout">
                <Content className="public-workspace-content">
                    <div className="loading-container">
                        <Spin size="large" />
                        <Text style={{ marginTop: 16, display: "block" }}>
                            Đang tải workspace...
                        </Text>
                    </div>
                </Content>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout className="public-workspace-layout">
                <Content className="public-workspace-content">
                    <div className="error-container">
                        <Alert
                            message="Không thể truy cập workspace"
                            description={error}
                            type="error"
                            showIcon
                        />
                    </div>
                </Content>
            </Layout>
        );
    }

    if (!workspace) {
        return (
            <Layout className="public-workspace-layout">
                <Content className="public-workspace-content">
                    <div className="error-container">
                        <Empty description="Workspace không tồn tại hoặc không được chia sẻ công khai" />
                    </div>
                </Content>
            </Layout>
        );
    }

    // Render board view with tasklists
    if (viewMode === "board" && selectedBoard) {
        return (
            <Layout className="public-workspace-layout">
                <Header className="public-workspace-header">
                    <div className="header-content">
                        <div style={{ display: "flex", alignItems: "center" }}>
                            <Button
                                type="text"
                                icon={<ArrowLeftOutlined />}
                                onClick={handleBackToWorkspace}
                                style={{
                                    color: "#374151",
                                    marginRight: "16px",
                                }}
                            ></Button>
                            <div className="logo">
                                <img
                                    src={"/gwtask.png"}
                                    style={{ width: "24px", height: "24px" }}
                                    alt="GW Tasks"
                                />
                                <span className="logo-text">GW Tasks</span>
                            </div>
                        </div>
                        <Button
                            type="primary"
                            onClick={handleLogin}
                            loading={isLoading}
                            style={{
                                backgroundColor: "#667eea",
                                borderColor: "#667eea",
                            }}
                        >
                            {isLoading ? "Đang xử lý..." : "Đăng nhập"}
                        </Button>
                    </div>
                </Header>

                <Content className="public-workspace-content">
                    {taskListsLoading ? (
                        <div className="loading-container">
                            <Spin size="large" />
                            <Text style={{ marginTop: 16, display: "block" }}>
                                Đang tải danh sách task...
                            </Text>
                        </div>
                    ) : taskListsError ? (
                        <Alert
                            message="Không thể tải danh sách task"
                            description={taskListsError}
                            type="error"
                            showIcon
                        />
                    ) : (
                        <div className="board-view-wrapper">
                            <div
                                className="board-view-container"
                                style={{
                                    display: "flex",
                                    overflowX: "auto",
                                    padding: "24px",
                                    gap: "20px",
                                    minHeight: "calc(100vh - 70px)",
                                    background: "#f5f5f5",
                                }}
                            >
                                {taskListsLoading ? (
                                    // Show loading skeletons
                                    [1, 2, 3].map((index) => (
                                        <ReadOnlyTaskList
                                            key={`loading-${index}`}
                                            taskList={{ tasks: [] }}
                                            loading={true}
                                        />
                                    ))
                                ) : taskLists.length > 0 ? (
                                    taskLists.map((taskList) => (
                                        <ReadOnlyTaskList
                                            key={taskList.id}
                                            taskList={taskList}
                                            loading={false}
                                            onTaskDoubleClick={
                                                handleTaskDoubleClick
                                            }
                                        />
                                    ))
                                ) : (
                                    <Empty
                                        description="Board này chưa có task list nào"
                                        style={{ margin: "auto" }}
                                    />
                                )}
                            </div>
                        </div>
                    )}
                </Content>

                {/* Task Detail Modal */}
                <TaskDetailModal
                    task={selectedTask}
                    visible={taskModalVisible}
                    onClose={handleCloseTaskModal}
                />
            </Layout>
        );
    }

    return (
        <Layout className="public-workspace-layout">
            <Header className="public-workspace-header">
                <div className="header-content">
                    <div className="logo">
                        <img
                            src={"/gwtask.png"}
                            style={{ width: "24px", height: "24px" }}
                            alt="GW Tasks"
                        />
                        <span className="logo-text">GW Tasks</span>
                    </div>
                    <Button
                        type="primary"
                        onClick={handleLogin}
                        loading={isLoading}
                        style={{
                            backgroundColor: "#667eea",
                            borderColor: "#667eea",
                        }}
                    >
                        {isLoading ? "Đang xử lý..." : "Đăng nhập"}
                    </Button>
                </div>
            </Header>

            <Content className="public-workspace-content">
                <div className="workspace-container">
                    {/* Workspace Info Card */}
                    <Card className="workspace-info-card">
                        <div className="workspace-header">
                            <div className="workspace-description-section">
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        marginBottom: "16px",
                                    }}
                                >
                                    <Title
                                        level={4}
                                        style={{ margin: 0, color: "#374151" }}
                                    >
                                        {workspace.name}
                                    </Title>
                                </div>

                                {workspace.description ? (
                                    <Paragraph
                                        style={{
                                            color: "#6b7280",
                                            fontSize: "16px",
                                            lineHeight: "1.6",
                                        }}
                                    >
                                        {workspace.description}
                                    </Paragraph>
                                ) : (
                                    <Paragraph
                                        style={{
                                            color: "#9ca3af",
                                            fontSize: "16px",
                                            fontStyle: "italic",
                                        }}
                                    >
                                        Chưa có mô tả cho workspace này.
                                    </Paragraph>
                                )}
                            </div>
                        </div>

                        <Divider />

                        {/* Owner Info */}
                        <div className="owner-section">
                            <Text
                                strong
                                style={{ fontSize: "16px", color: "#374151" }}
                            >
                                <UserOutlined
                                    style={{ marginRight: 8, color: "#667eea" }}
                                />
                                Người sở hữu
                            </Text>
                            <div style={{ marginTop: 8 }}>
                                <Space>
                                    <Avatar
                                        src={workspace.owner?.photoUrl}
                                        icon={<UserOutlined />}
                                        size="small"
                                    />
                                    <Text>
                                        {workspace.owner?.fullName ||
                                            workspace.owner?.email}
                                    </Text>
                                </Space>
                            </div>
                        </div>

                        <Divider />

                        {/* Boards Section */}
                        <div className="boards-section">
                            <Text
                                strong
                                style={{ fontSize: "16px", color: "#374151" }}
                            >
                                <FolderOutlined
                                    style={{ marginRight: 8, color: "#667eea" }}
                                />
                                Danh sách Board ({workspace.boards?.length || 0}
                                )
                            </Text>

                            {workspace.boards && workspace.boards.length > 0 ? (
                                <div className="boards-grid">
                                    {workspace.boards.map((board) => (
                                        <Card
                                            key={board.id}
                                            size="small"
                                            className="board-card"
                                            hoverable
                                            onClick={() =>
                                                handleBoardSelect(board)
                                            }
                                            style={{ cursor: "pointer" }}
                                        >
                                            <div className="board-info">
                                                <Text strong>{board.name}</Text>
                                                {board.isGoogleSynced && (
                                                    <Tag
                                                        color="blue"
                                                        size="small"
                                                        style={{
                                                            marginLeft: 8,
                                                        }}
                                                    >
                                                        Google Sync
                                                    </Tag>
                                                )}
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <Empty
                                    description="Chưa có board nào"
                                    style={{ marginTop: 16 }}
                                />
                            )}
                        </div>
                    </Card>

                    {/* Info Notice */}
                    <Alert
                        message="Chế độ xem công khai"
                        description="Bạn đang xem workspace ở chế độ chỉ đọc.
                        Để tương tác với workspace này, vui lòng đăng nhập và được chủ sở hữu thêm vào workspace."
                        type="info"
                        showIcon
                        style={{ marginTop: 16 }}
                    />
                </div>
            </Content>

            {/* Task Detail Modal */}
            <TaskDetailModal
                task={selectedTask}
                visible={taskModalVisible}
                onClose={handleCloseTaskModal}
            />
        </Layout>
    );
};

export default PublicWorkspace;
