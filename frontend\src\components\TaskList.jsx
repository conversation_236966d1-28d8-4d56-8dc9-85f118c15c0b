import React from 'react';
import { Badge, Input, Button, Dropdown, Popconfirm } from 'antd';
import { MoreOutlined, DeleteOutlined } from '@ant-design/icons';
import TaskWithChildren from './TaskWithChildren';
import AddTaskCard from './AddTaskCard';
import {
    SortableContext,
    useSortable,
    verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import useTask from '../hooks/useTask';

const TaskList = ({
    boardId,
    workspaceId,
    taskList,
    onDeleteTaskList,
    onUpdateTaskList,
    onModalStateChange,
    boardMembers = null,
    refreshBoards,
    searchText,
    setSearchResult,
    isUserWorkspaceOwner
}) => {
    const {
        editingList,
        editingDescription,
        tasks,
        organizedTasks,
        isAnyModalOpen,
        setEditingList,
        setEditingDescription,
        setIsAnyModalOpen,
        handleUpdateListTitle,
        handleUpdateDescription,
        addNewTask,
        deleteTask,
        updateTask,
        addSubtask,
        toggleTaskComplete
    } = useTask(taskList, boardId, onUpdateTaskList, refreshBoards);

    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging
    } = useSortable({
        id: taskList.id,
        disabled:
            isAnyModalOpen ||
            editingList === taskList.id ||
            editingDescription === taskList.id,
        data: {
            type: 'taskList',
            taskList
        }
    });

    // Handle modal state change từ các TaskCard con
    const handleModalStateChange = (isOpen) => {
        setIsAnyModalOpen(isOpen);
        // Truyền state lên Dashboard
        if (onModalStateChange) {
            onModalStateChange(isOpen);
        }
    };

    const listMenuItems = [
        {
            key: 'delete',
            label: (
                <Popconfirm
                    title='Xác nhận xóa Task list'
                    description='Bạn có chắc chắn muốn xóa?'
                    onConfirm={() => onDeleteTaskList(taskList.id)}
                    okText='Xóa'
                    cancelText='Hủy'
                    okType='danger'
                    placement='bottom'
                >
                    <div className='dropdown-item delete-item'>
                        <DeleteOutlined />
                        <span>Xóa</span>
                    </div>
                </Popconfirm>
            )
        }
    ];

    const style = {
        touchAction: 'none',
        transition,
        transform: CSS.Transform.toString(transform),
        opacity: isDragging ? 0.5 : undefined,
        border: isDragging ? '1px dashed #ccc' : 'none',
        cursor: isDragging ? 'grabbing' : 'grab'
    };

    return (
        <div
            className='board-column'
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
        >
            <div className='column-header'>
                <div className='column-title'>
                    <div
                        className='list-color'
                        style={{
                            backgroundColor: taskList.color || '#1890FF',
                            width: '12px',
                            height: '12px',
                            borderRadius: '50%',
                            marginRight: '8px'
                        }}
                    ></div>
                    <div className='list-title-section'>
                        {editingList === taskList.id ? (
                            <Input
                                defaultValue={taskList.title}
                                className='list-name-input'
                                size='small'
                                onPressEnter={(e) =>
                                    handleUpdateListTitle(e.target.value)
                                }
                                onBlur={(e) =>
                                    handleUpdateListTitle(e.target.value)
                                }
                                onKeyDown={(e) => {
                                    if (e.key === 'Escape') {
                                        setEditingList(null);
                                    }
                                }}
                                autoFocus
                            />
                        ) : (
                            <h3
                                className='list-name editable'
                                onClick={() => setEditingList(taskList.id)}
                            >
                                {taskList.title}
                            </h3>
                        )}

                        {/* Description Section */}
                        {editingDescription === taskList.id ? (
                            <Input
                                defaultValue={taskList.description || ''}
                                className='list-description-input'
                                size='small'
                                placeholder='Thêm mô tả...'
                                onPressEnter={(e) =>
                                    handleUpdateDescription(e.target.value)
                                }
                                onBlur={(e) =>
                                    handleUpdateDescription(e.target.value)
                                }
                                onKeyDown={(e) => {
                                    if (e.key === 'Escape') {
                                        setEditingDescription(null);
                                    }
                                }}
                                autoFocus
                            />
                        ) : (
                            <p
                                className='list-description editable'
                                onClick={() =>
                                    setEditingDescription(taskList.id)
                                }
                                style={{
                                    margin: '4px 0 0 0',
                                    fontSize: '12px',
                                    color: '#666',
                                    cursor: 'pointer',
                                    minHeight: '18px',
                                    fontStyle: taskList.description
                                        ? 'normal'
                                        : 'italic'
                                }}
                            >
                                {taskList.description || 'Thêm mô tả...'}
                            </p>
                        )}
                    </div>
                    {/* <Badge count={taskList.tasks?.length || 0} /> */}
                </div>
                {/* Hiển thị nút xóa nếu không phải task list mặc định */}
                {taskList.isDefault ? null : (
                    <Dropdown
                        menu={{ items: listMenuItems }}
                        trigger={['click']}
                        placement='bottomRight'
                    >
                        <Button
                            type='text'
                            icon={<MoreOutlined />}
                            size='small'
                            className='list-menu-btn'
                        />
                    </Dropdown>
                )}
            </div>
            <div className='column-content'>
                <SortableContext
                    items={tasks.map((task) => task.id)}
                    strategy={verticalListSortingStrategy}
                >
                    {organizedTasks.map((task) => (
                        <TaskWithChildren
                            key={task.id}
                            task={task}
                            listId={taskList.id}
                            boardId={boardId}
                            workspaceId={workspaceId}
                            onUpdateTask={updateTask}
                            onDeleteTask={deleteTask}
                            onAddSubtask={addSubtask}
                            onToggleTaskComplete={toggleTaskComplete}
                            onModalStateChange={handleModalStateChange}
                            boardMembers={boardMembers}
                            searchText={searchText}
                            setSearchResult={setSearchResult}
                        />
                    ))}
                </SortableContext>
                <AddTaskCard listId={taskList.id} onAddTask={addNewTask} />
            </div>
        </div>
    );
};

export default TaskList;
