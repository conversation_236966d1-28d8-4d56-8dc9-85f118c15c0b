const { DataTypes } = require("sequelize");

const Package = {
    FREE: "1",
    PRO: "2",
    TEAM_WORK: "3",
};

const LimitBoard = {
    FREE: 5,
    PRO: 0,
    TEAM_WORK: 0,
};

const LimitMember = {
    FREE: 0,
    PRO: 0,
    TEAM_WORK: 20,
};

module.exports = (sequelize, DataTypes) => {
    const Workspace = sequelize.define(
        "Workspace",
        {
            id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            ownerId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            isDefault: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            package: {
                type: DataTypes.STRING,
                defaultValue: Package.FREE,
            },
            teamFeature: {
                type: DataTypes.INTEGER,
                defaultValue: 0,
            },
            limitMember: {
                type: DataTypes.INTEGER,
                defaultValue: LimitMember.FREE,
            },
            limitBoard: {
                type: DataTypes.INTEGER,
                defaultValue: LimitBoard.FREE,
            },
            publicShareId: {
                type: DataTypes.UUID,
                unique: true,
                allowNull: true,
            },
            isPublicShareEnabled: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
            },
        },
        {
            tableName: "workspaces",
            timestamps: true,
        }
    );

    Workspace.associate = (models) => {
        Workspace.belongsTo(models.User, {
            foreignKey: "ownerId",
            as: "owner",
        });
        Workspace.hasMany(models.Board, {
            foreignKey: "workspaceId",
            as: "boards",
            onDelete: "CASCADE",
        });
        Workspace.hasMany(models.WorkspaceAssignee, {
            foreignKey: "workspace_id",
            as: "workspaceAssignees",
            onDelete: "CASCADE",
        });
    };

    return Workspace;
};
