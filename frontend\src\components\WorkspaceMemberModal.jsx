import React, { useState, useEffect } from "react";
import {
    Mo<PERSON>,
    List,
    Avatar,
    Button,
    Input,
    message,
    Spin,
    Empty,
    Popconfirm,
    Tag,
    Space,
    Typography,
} from "antd";
import {
    UserOutlined,
    DeleteOutlined,
    PlusOutlined,
    SearchOutlined,
} from "@ant-design/icons";
import workspaceAssignService from "../services/workspaceAssignService";
import userService from "../services/userService";

const { Search } = Input;
const { Text } = Typography;

const WorkspaceMemberModal = ({
    visible,
    onClose,
    workspaceId,
    workspaceName,
    onMemberRemoved,
    onMemberAdded,
}) => {
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(false);
    const [searchResults, setSearchResults] = useState([]);
    const [searching, setSearching] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");

    // Fetch workspace members
    const fetchMembers = async () => {
        if (!workspaceId) return;

        try {
            setLoading(true);
            const response = await workspaceAssignService.getWorkspaceAssignees(
                workspaceId
            );
            // console.log('Members response:', response);
            setMembers(response);
        } catch (error) {
            console.error("Error loading workspace members:", error);
            message.error("Không thể tải danh sách thành viên workspace");
        } finally {
            setLoading(false);
        }
    };

    // Search users
    const handleSearch = async (value) => {
        if (!value || value.length < 2) {
            setSearchResults([]);
            return;
        }

        try {
            setSearching(true);
            setSearchQuery(value);
            const response = await userService.searchUsers(value);

            // Filter out users who are already members
            const memberIds = members.map((m) => m.User?.id || m.userId);
            const filteredResults = response.filter(
                (user) => !memberIds.includes(user.id)
            );

            setSearchResults(filteredResults);
        } catch (error) {
            console.error("Error searching users:", error);
            message.error("Không thể tìm kiếm người dùng");
        } finally {
            setSearching(false);
        }
    };

    // Add member to workspace
    const handleAddMember = async (user) => {
        try {
            await workspaceAssignService.assignUserToWorkspace(
                user.id,
                workspaceId
            );
            message.success(`Đã thêm ${user.name || user.email} vào workspace`);

            // Refresh members list
            fetchMembers();

            // Call callback to refresh workspace members in MainLayout
            if (onMemberAdded) {
                onMemberAdded();
            }

            // Dispatch event để các component khác có thể lắng nghe
            window.dispatchEvent(
                new CustomEvent("workspace:member_added", {
                    detail: { workspaceId },
                })
            );

            // Clear search
            setSearchQuery("");
            setSearchResults([]);
        } catch (error) {
            console.error("Error adding member:", error);
            message.error("Không thể thêm thành viên");
        }
    };

    // Remove member from workspace
    const handleRemoveMember = async (member) => {
        try {
            const userId = member.User?.id || member.userId;
            await workspaceAssignService.unassignUserFromWorkspace(
                userId,
                workspaceId
            );
            message.success(
                "Đã xóa thành viên khỏi workspace và tất cả Board và Task liên quan"
            );

            // Refresh members list
            fetchMembers();

            // Call callback to refresh other data
            if (onMemberRemoved) {
                onMemberRemoved();
            }
        } catch (error) {
            console.error("Error removing member:", error);
            message.error("Không thể xóa thành viên");
        }
    };

    useEffect(() => {
        if (visible && workspaceId) {
            fetchMembers();
        }
    }, [visible, workspaceId]);

    return (
        <Modal
            title={
                <Space>
                    <UserOutlined />
                    <span>Quản lý thành viên - {workspaceName}</span>
                </Space>
            }
            open={visible}
            onCancel={onClose}
            footer={null}
            width={600}
            style={{ padding: "20px" }}
        >
            <div style={{ marginBottom: "20px" }}>
                <Search
                    placeholder="Tìm kiếm người dùng để thêm vào workspace..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onSearch={handleSearch}
                    loading={searching}
                    prefix={<SearchOutlined />}
                    allowClear
                />

                {/* Search Results */}
                {searchResults.length > 0 && (
                    <div
                        style={{
                            marginTop: "12px",
                            border: "1px solid #d9d9d9",
                            borderRadius: "6px",
                            maxHeight: "200px",
                            overflowY: "auto",
                        }}
                    >
                        <List
                            size="small"
                            dataSource={searchResults}
                            renderItem={(user) => (
                                <List.Item
                                    style={{ padding: "8px 12px" }}
                                    actions={[
                                        <Button
                                            type="link"
                                            size="small"
                                            icon={<PlusOutlined />}
                                            onClick={() =>
                                                handleAddMember(user)
                                            }
                                        >
                                            Thêm
                                        </Button>,
                                    ]}
                                >
                                    <List.Item.Meta
                                        avatar={
                                            <Avatar
                                                src={user.photoUrl}
                                                icon={<UserOutlined />}
                                                size="small"
                                            />
                                        }
                                        title={user.fullName || "Không có tên"}
                                        description={user.email}
                                    />
                                </List.Item>
                            )}
                        />
                    </div>
                )}
            </div>

            <div>
                <Text
                    strong
                    style={{
                        fontSize: "16px",
                        marginBottom: "12px",
                        display: "block",
                    }}
                >
                    Thành viên trong workspace ({members.length})
                </Text>

                {loading ? (
                    <div style={{ textAlign: "center", padding: "40px" }}>
                        <Spin size="large" />
                    </div>
                ) : members.length === 0 ? (
                    <Empty
                        description="Không có thành viên trong workspace"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                ) : (
                    <List
                        dataSource={members}
                        renderItem={(member) => {
                            const user = member.user || member;
                            const isCurrentUser =
                                user.id ===
                                JSON.parse(localStorage.getItem("user"))?.id;

                            return (
                                <List.Item
                                    actions={[
                                        !isCurrentUser && (
                                            <Popconfirm
                                                title="Xóa thành viên"
                                                description="Bạn có chắc chắn muốn xóa thành viên này khỏi workspace?"
                                                onConfirm={() =>
                                                    handleRemoveMember(member)
                                                }
                                                okText="Xóa"
                                                cancelText="Hủy"
                                                okType="danger"
                                            >
                                                <Button
                                                    type="text"
                                                    danger
                                                    size="small"
                                                    icon={<DeleteOutlined />}
                                                >
                                                    Xóa
                                                </Button>
                                            </Popconfirm>
                                        ),
                                    ].filter(Boolean)}
                                >
                                    <List.Item.Meta
                                        avatar={
                                            <Avatar
                                                src={user.photoUrl}
                                                icon={<UserOutlined />}
                                            />
                                        }
                                        title={
                                            <Space>
                                                {user.fullName ||
                                                    "Không có tên"}
                                                {isCurrentUser && (
                                                    <Tag
                                                        color="blue"
                                                        size="small"
                                                    >
                                                        Bạn
                                                    </Tag>
                                                )}
                                            </Space>
                                        }
                                        description={user.email}
                                    />
                                </List.Item>
                            );
                        }}
                    />
                )}
            </div>
        </Modal>
    );
};

export default WorkspaceMemberModal;
