import React from "react";
import { Tag, Tooltip } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import "./LabelComponents.css";

const LabelBadge = ({
  label,
  size = "small",
  showClose = false,
  onClose,
  onClick,
  style = {},
  className = "",
}) => {
  const handleClose = (e) => {
    e.stopPropagation();
    if (onClose) {
      onClose(label);
    }
  };

  const handleClick = (e) => {
    e.stopPropagation();
    if (onClick) {
      onClick(label);
    }
  };

  // Tính toán màu text dựa trên màu nền
  const getTextColor = (backgroundColor) => {
    if (!backgroundColor) return "#000";

    // Chuyển hex sang RGB
    const hex = backgroundColor.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // T<PERSON>h độ sáng
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Trả về màu text phù hợp
    return brightness > 128 ? "#000" : "#fff";
  };

  const tagStyle = {
    backgroundColor: label.color || "#f0f0f0",
    color: getTextColor(label.color),
    border: `1px solid ${label.color || "#d9d9d9"}`,
    borderRadius: "4px",
    margin: "2px",
    cursor: onClick ? "pointer" : "default",
    ...style,
  };

  const tagProps = {
    style: tagStyle,
    className: `label-badge ${className}`,
    onClick: onClick ? handleClick : undefined,
  };

  if (showClose) {
    tagProps.closable = true;
    tagProps.closeIcon = <CloseOutlined style={{ fontSize: "10px" }} />;
    tagProps.onClose = handleClose;
  }

  return (
    <Tooltip title={label.name} placement="top">
      <Tag {...tagProps}>{label.name}</Tag>
    </Tooltip>
  );
};

export default LabelBadge;
