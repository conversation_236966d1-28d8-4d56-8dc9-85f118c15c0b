import { useState } from "react";
import { Card, Avatar, Tag, Space, Typography } from "antd";
import {
    CalendarOutlined,
    UserOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    FlagOutlined,
} from "@ant-design/icons";
import { formatDate } from "../utils/date";
import "../pages/DashBoard.css";

const { Text } = Typography;

const ReadOnlyTaskCard = ({
    task,
    level = 0,
    isParent = false,
    isChildTask = false,
    childrenTasks = [],
    isExpanded = true,
    onTaskDoubleClick,
}) => {
    const [expanded, setExpanded] = useState(isExpanded);

    const isCompleted = task.status === "completed";

    const getPriorityColor = (priority) => {
        switch (priority) {
            case "high":
                return "#ff4d4f";
            case "medium":
                return "#faad14";
            case "low":
                return "#52c41a";
            default:
                return "#d9d9d9";
        }
    };

    const getPriorityText = (priority) => {
        switch (priority) {
            case "high":
                return "Cao";
            case "medium":
                return "Trung bình";
            case "low":
                return "Thấp";
            default:
                return "Không xác định";
        }
    };

    const handleToggleExpanded = () => {
        if (isParent && childrenTasks.length > 0) {
            setExpanded(!expanded);
        }
    };

    const handleDoubleClick = () => {
        if (onTaskDoubleClick) {
            onTaskDoubleClick(task);
        }
    };

    // Sử dụng style giống dashboard
    const cardStyle = {
        marginBottom: 8,
        marginLeft: isChildTask ? 20 : 0,
        position: "relative",
        ...(isChildTask && {
            border: "1px solid #e8e8e8",
            borderLeft: "4px solid #1890ff",
            backgroundColor: "#fafafa",
            borderRadius: "6px",
            boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        }),
        ...(isCompleted && {
            backgroundColor: "#f5f5f5",
            opacity: 0.7,
        }),
    };

    return (
        <>
            <Card
                className={`task-card ${isChildTask ? "subtask-card" : ""} ${
                    isCompleted ? "completed-task" : ""
                } ${isParent ? "has-children" : ""}`}
                size="small"
                style={{
                    ...cardStyle,
                    cursor: "pointer",
                }}
                onDoubleClick={handleDoubleClick}
            >
                <div className="task-header">
                    {/* Priority indicator */}
                    {task.priority && task.priority !== "none" && (
                        <div
                            className="task-priority"
                            style={{
                                backgroundColor: getPriorityColor(
                                    task.priority
                                ),
                            }}
                        ></div>
                    )}

                    <div
                        className="task-title-section"
                        style={{
                            display: "flex",
                            alignItems: "center",
                            flex: 1,
                            minWidth: 0, // Cho phép flex item shrink
                        }}
                    >
                        {isParent && childrenTasks.length > 0 && (
                            <span
                                className="expand-icon"
                                onClick={handleToggleExpanded}
                                style={{
                                    cursor: "pointer",
                                    marginRight: "8px",
                                    fontSize: "12px",
                                    color: "#8c8c8c",
                                }}
                            >
                                {expanded ? "▼" : "▶"}
                            </span>
                        )}

                        <Text
                            className={`task-title ${
                                isChildTask ? "subtask-title" : ""
                            }`}
                            style={{
                                textDecoration: isCompleted
                                    ? "line-through"
                                    : "none",
                                color: isCompleted ? "#8c8c8c" : "inherit",
                                flex: 1,
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                marginRight: "8px",
                            }}
                        >
                            {task.title}
                        </Text>

                        <div
                            className="task-status-icon"
                            style={{ marginLeft: "auto" }}
                        >
                            {isCompleted ? (
                                <CheckCircleOutlined
                                    style={{
                                        color: "#52c41a",
                                        fontSize: "14px",
                                    }}
                                />
                            ) : (
                                <ClockCircleOutlined
                                    style={{
                                        color: "#faad14",
                                        fontSize: "14px",
                                    }}
                                />
                            )}
                        </div>
                    </div>
                </div>

                {task.notes && (
                    <div className="task-notes" style={{ marginTop: "8px" }}>
                        <Text
                            type="secondary"
                            style={{
                                fontSize: "12px",
                                color: isCompleted ? "#8c8c8c" : "#666",
                                display: "block",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                            }}
                        >
                            {task.notes}
                        </Text>
                    </div>
                )}

                <div className="task-footer" style={{ marginTop: "8px" }}>
                    <Space size="small" wrap>
                        {task.assignee && (
                            <div className="task-assignee">
                                <Avatar
                                    size="small"
                                    src={task.assignee.photoUrl}
                                    icon={<UserOutlined />}
                                />
                                <Text
                                    style={{
                                        marginLeft: "4px",
                                        fontSize: "12px",
                                    }}
                                >
                                    {task.assignee.fullName ||
                                        task.assignee.email}
                                </Text>
                            </div>
                        )}
                    </Space>
                </div>

                {task.due && (
                    <div className="task-due-date" style={{ marginTop: "4px" }}>
                        <CalendarOutlined
                            style={{
                                marginRight: "4px",
                                color: isCompleted ? "#8c8c8c" : undefined,
                            }}
                        />
                        <span
                            className="date-text"
                            style={{
                                fontSize: "12px",
                                color: isCompleted ? "#8c8c8c" : "#666",
                            }}
                        >
                            {formatDate(task.due)}
                        </span>
                    </div>
                )}
            </Card>

            {/* Render child tasks if expanded */}
            {expanded &&
                isParent &&
                childrenTasks &&
                childrenTasks.length > 0 && (
                    <div style={{ marginLeft: "20px" }}>
                        {childrenTasks.map((childTask) => (
                            <ReadOnlyTaskCard
                                key={childTask.id}
                                task={childTask}
                                level={level + 1}
                                isChildTask={true}
                                onTaskDoubleClick={onTaskDoubleClick}
                            />
                        ))}
                    </div>
                )}
        </>
    );
};

export default ReadOnlyTaskCard;
