import React, { useState, useEffect } from "react";
import {
    Button,
    Modal,
    Input,
    Form,
    message,
    Dropdown,
    Space,
    Tag,
    Popconfirm,
} from "antd";
import { useSocket } from "../contexts/SocketContext";
import {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    DownOutlined,
    UserOutlined,
    MoreOutlined,
    LogoutOutlined,
    ShareAltOutlined,
    CopyOutlined,
    EyeInvisibleOutlined,
} from "@ant-design/icons";
import workspaceService from "../services/workspaceService";

const WorkspaceSelector = ({
    workspaces,
    selectedWorkspace,
    onSelectWorkspace,
    onCreateWorkspace,
    onUpdateWorkspace,
    onDeleteWorkspace,
    onManageMembers,
    leaveAssignedWorkspace,
    loading,
}) => {
    const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [editingWorkspace, setEditingWorkspace] = useState(null);
    const [createLoading, setCreateLoading] = useState(false);
    const [createForm] = Form.useForm();
    const [editForm] = Form.useForm();
    const currentWorkspace = workspaces.find((w) => w.id === selectedWorkspace);
    const { joinWorkspace, leaveWorkspace } = useSocket();

    // Quản lý join/leave workspace thông qua socket
    useEffect(() => {
        if (selectedWorkspace) {
            // Join workspace mới
            joinWorkspace(selectedWorkspace);
        }

        // Cleanup function khi component unmount hoặc selectedWorkspace thay đổi
        return () => {
            if (selectedWorkspace) {
                leaveWorkspace(selectedWorkspace);
            }
        };
    }, [selectedWorkspace, joinWorkspace, leaveWorkspace]);

    // Hàm cắt ngắn văn bản
    const truncateText = (text, maxLength = 10) => {
        if (!text) return "";
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + "...";
    };

    const handleSelectWorkspace = (workspaceId) => {
        // If there was a previously selected workspace, leave that workspace
        if (selectedWorkspace && selectedWorkspace !== workspaceId) {
            leaveWorkspace(selectedWorkspace);
        }
        // Join workspace mới và cập nhật selection
        onSelectWorkspace(workspaceId);
    };

    const handleCreateWorkspace = async (values) => {
        setCreateLoading(true);
        try {
            await onCreateWorkspace(values);
            setIsCreateModalVisible(false);
            createForm.resetFields();
            message.success("Workspace đã được tạo thành công!");
        } catch (error) {
            message.error("Lỗi khi tạo workspace");
        } finally {
            setCreateLoading(false);
        }
    };

    const handleEditWorkspace = async (values) => {
        try {
            await onUpdateWorkspace(editingWorkspace.id, values);
            setIsEditModalVisible(false);
            setEditingWorkspace(null);
            editForm.resetFields();
            message.success("Workspace đã được cập nhật thành công!");
        } catch (error) {
            message.error("Lỗi khi cập nhật workspace");
        }
    };

    const handleDeleteWorkspace = async (workspaceId) => {
        if (onDeleteWorkspace) {
            await onDeleteWorkspace(workspaceId);
        }
    };

    const openEditModal = (workspace) => {
        setEditingWorkspace(workspace);
        editForm.setFieldsValue({
            name: workspace.name,
            description: workspace.description,
        });
        setIsEditModalVisible(true);
    };

    // Hàm xử lý public sharing
    const handleEnablePublicShare = async (workspace) => {
        try {
            const updatedWorkspace = await workspaceService.enablePublicShare(
                workspace.id
            );
            message.success("Đã kích hoạt chia sẻ công khai");

            // Cập nhật workspace trong danh sách
            if (onUpdateWorkspace) {
                onUpdateWorkspace(workspace.id, updatedWorkspace);
            }
        } catch (error) {
            console.error("Error enabling public share:", error);
            message.error("Không thể kích hoạt chia sẻ công khai");
        }
    };

    const handleDisablePublicShare = async (workspace) => {
        try {
            await workspaceService.disablePublicShare(workspace.id);
            message.success("Đã vô hiệu hóa chia sẻ công khai");

            // Cập nhật workspace trong danh sách
            if (onUpdateWorkspace) {
                onUpdateWorkspace(workspace.id, {
                    ...workspace,
                    isPublicShareEnabled: false,
                });
            }
        } catch (error) {
            console.error("Error disabling public share:", error);
            message.error("Không thể vô hiệu hóa chia sẻ công khai");
        }
    };

    const handleCopyPublicLink = (workspace) => {
        if (workspace.publicShareId) {
            const publicUrl = `${window.location.origin}/public/workspace/${workspace.publicShareId}`;
            navigator.clipboard
                .writeText(publicUrl)
                .then(() => {
                    message.success("Đã sao chép link chia sẻ");
                })
                .catch(() => {
                    message.error("Không thể sao chép link");
                });
        }
    };

    // Hàm tạo menu hành động cho workspace
    const getWorkspaceActionMenu = (workspace) => {
        const actionItems = [];

        // Chỉ cho phép chỉnh sửa nếu user là owner
        if (workspace.isOwned) {
            actionItems.push({
                key: `edit-${workspace.id}`,
                label: (
                    <div className="workspace-action-item">
                        <EditOutlined />
                        <span>Chỉnh sửa</span>
                    </div>
                ),
                onClick: () => {
                    openEditModal(workspace);
                },
            });

            actionItems.push({
                key: `members-${workspace.id}`,
                label: (
                    <div className="workspace-action-item">
                        <UserOutlined />
                        <span>Quản lý thành viên</span>
                    </div>
                ),
                onClick: () => {
                    onManageMembers && onManageMembers(workspace);
                },
            });

            // Public sharing menu items
            if (workspace.isPublicShareEnabled) {
                actionItems.push({
                    key: `copy-link-${workspace.id}`,
                    label: (
                        <div className="workspace-action-item">
                            <CopyOutlined />
                            <span>Sao chép link chia sẻ</span>
                        </div>
                    ),
                    onClick: () => {
                        handleCopyPublicLink(workspace);
                    },
                });

                actionItems.push({
                    key: `disable-share-${workspace.id}`,
                    label: (
                        <div className="workspace-action-item">
                            <EyeInvisibleOutlined />
                            <span>Tắt chia sẻ công khai</span>
                        </div>
                    ),
                    onClick: () => {
                        handleDisablePublicShare(workspace);
                    },
                });
            } else {
                actionItems.push({
                    key: `enable-share-${workspace.id}`,
                    label: (
                        <div className="workspace-action-item">
                            <ShareAltOutlined />
                            <span>Chia sẻ công khai</span>
                        </div>
                    ),
                    onClick: () => {
                        handleEnablePublicShare(workspace);
                    },
                });
            }

            //Chỉ hiển thị nút xóa nếu không phải workspace mặc định
            if (!workspace.isDefault) {
                actionItems.push({
                    key: `delete-${workspace.id}`,
                    label: (
                        <Popconfirm
                            title="Xóa workspace"
                            description={`Bạn có chắc chắn muốn xóa workspace "${workspace.name}"?`}
                            onConfirm={() =>
                                handleDeleteWorkspace(workspace.id)
                            }
                            okText="Xóa"
                            cancelText="Hủy"
                            okType="danger"
                            placement="left"
                        >
                            <div className="workspace-action-item delete-action">
                                <DeleteOutlined />
                                <span>Xóa workspace</span>
                            </div>
                        </Popconfirm>
                    ),
                });
            }
        } else {
            actionItems.push({
                key: `leave-${workspace.id}`,
                label: (
                    <Popconfirm
                        title="Rời khỏi workspace"
                        description={`Bạn có chắc chắn muốn rời khỏi workspace "${workspace.name}"?`}
                        onConfirm={() => leaveAssignedWorkspace(workspace.id)}
                        okText="Rời khỏi"
                        cancelText="Hủy"
                        okType="danger"
                        placement="left"
                    >
                        <div className="workspace-action-item">
                            <LogoutOutlined />
                            <span>Rời khỏi workspace</span>
                        </div>
                    </Popconfirm>
                ),
            });
        }

        return { items: actionItems };
    };

    // Nhóm workspace items
    const workspaceItems = workspaces.map((workspace) => ({
        key: `workspace-${workspace.id}`,
        label: (
            <div
                className="workspace-dropdown-item"
                onClick={() => onSelectWorkspace(workspace.id)}
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "8px 12px",
                    cursor: "pointer",
                }}
            >
                <div style={{ display: "flex", alignItems: "center", flex: 1 }}>
                    <div className="workspace-avatar-small">
                        {workspace.name.charAt(0).toUpperCase()}
                    </div>
                    <div
                        className="workspace-info-dropdown"
                        style={{ marginLeft: 12, flex: 1 }}
                    >
                        <div className="workspace-name-dropdown">
                            {truncateText(workspace.name)}
                            {workspace.isOwned && (
                                <Tag
                                    //icon={<CrownOutlined />}
                                    color="gold"
                                    size="small"
                                    style={{ marginLeft: 8, fontSize: "10px" }}
                                >
                                    Your
                                </Tag>
                            )}
                            {workspace.isAssigned && (
                                <Tag
                                    //icon={<TeamOutlined />}
                                    color="blue"
                                    size="small"
                                    style={{ marginLeft: 8, fontSize: "10px" }}
                                >
                                    Invited
                                </Tag>
                            )}
                        </div>
                        {workspace.description && (
                            <div className="workspace-description-dropdown">
                                {truncateText(workspace.description)}
                            </div>
                        )}
                    </div>
                </div>
                <div
                    className="workspace-actions"
                    style={{ display: "flex", alignItems: "center" }}
                    onClick={(e) => e.stopPropagation()}
                >
                    {selectedWorkspace === workspace.id && (
                        <div
                            className="workspace-selected-indicator"
                            style={{
                                marginRight: 8,
                                color: "#52c41a",
                                fontWeight: "bold",
                            }}
                        >
                            ✓
                        </div>
                    )}

                    <Dropdown
                        menu={getWorkspaceActionMenu(workspace)}
                        trigger={["click"]}
                        placement="bottomRight"
                    >
                        <Button
                            type="text"
                            size="small"
                            icon={<MoreOutlined />}
                            onClick={(e) => e.stopPropagation()}
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                opacity: 0.7,
                                transition: "opacity 0.2s",
                            }}
                            onMouseEnter={(e) => (e.target.style.opacity = 1)}
                            onMouseLeave={(e) => (e.target.style.opacity = 0.7)}
                        />
                    </Dropdown>
                </div>
            </div>
        ),
    }));

    const menuItems = [
        ...workspaceItems,
        { type: "divider" },
        {
            key: "create",
            label: (
                <div className="workspace-action-item create-action">
                    <PlusOutlined />
                    <span>Create new workspace</span>
                </div>
            ),
            onClick: () => setIsCreateModalVisible(true),
        },
    ];

    return (
        <>
            <div className="workspace-selector">
                <Dropdown
                    menu={{ items: menuItems }}
                    trigger={["click"]}
                    placement="bottomLeft"
                    overlayClassName="workspace-dropdown-overlay"
                >
                    <Button
                        type="text"
                        className="workspace-button"
                        loading={loading}
                    >
                        <Space>
                            <div className="workspace-avatar-small">
                                {(currentWorkspace?.name || "W")
                                    .charAt(0)
                                    .toUpperCase()}
                            </div>
                            <div className="workspace-info">
                                <div className="workspace-name">
                                    {truncateText(
                                        currentWorkspace?.name ||
                                            "Chọn không gian làm việc"
                                    )}
                                    {currentWorkspace?.isOwned && (
                                        <Tag
                                            //icon={<CrownOutlined />}
                                            color="gold"
                                            size="small"
                                            style={{
                                                marginLeft: 8,
                                                fontSize: "10px",
                                            }}
                                        >
                                            Your
                                        </Tag>
                                    )}
                                    {currentWorkspace?.isAssigned && (
                                        <Tag
                                            //icon={<TeamOutlined />}
                                            color="blue"
                                            size="small"
                                            style={{
                                                marginLeft: 8,
                                                fontSize: "10px",
                                            }}
                                        >
                                            Invited
                                        </Tag>
                                    )}
                                </div>
                                {currentWorkspace?.description && (
                                    <div className="workspace-description">
                                        {truncateText(
                                            currentWorkspace.description
                                        )}
                                    </div>
                                )}
                            </div>
                            <DownOutlined />
                        </Space>
                    </Button>
                </Dropdown>
            </div>

            {/* Create Workspace Modal */}
            <Modal
                title="Create new workspace"
                open={isCreateModalVisible}
                onCancel={() => {
                    setIsCreateModalVisible(false);
                    createForm.resetFields();
                }}
                footer={null}
                width={500}
            >
                <Form
                    form={createForm}
                    layout="vertical"
                    onFinish={handleCreateWorkspace}
                >
                    <Form.Item
                        name="name"
                        label="Tên workspace"
                        rules={[
                            {
                                required: true,
                                message: "Vui lòng nhập tên workspace",
                            },
                            {
                                max: 50,
                                message:
                                    "Tên workspace không được vượt quá 50 ký tự",
                            },
                        ]}
                    >
                        <Input placeholder="Nhập tên workspace" />
                    </Form.Item>

                    <Form.Item
                        name="description"
                        label="Mô tả (tùy chọn)"
                        rules={[
                            {
                                max: 200,
                                message: "Mô tả không được vượt quá 200 ký tự",
                            },
                        ]}
                    >
                        <Input.TextArea
                            placeholder="Mô tả workspace"
                            rows={3}
                        />
                    </Form.Item>

                    <Form.Item className="modal-actions">
                        <Space>
                            <Button
                                onClick={() => {
                                    setIsCreateModalVisible(false);
                                    createForm.resetFields();
                                }}
                                disabled={createLoading}
                            >
                                Hủy
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={createLoading}
                            >
                                Tạo workspace
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Edit Workspace Modal */}
            <Modal
                title="Chỉnh sửa workspace"
                open={isEditModalVisible}
                onCancel={() => {
                    setIsEditModalVisible(false);
                    setEditingWorkspace(null);
                    editForm.resetFields();
                }}
                footer={null}
                width={500}
            >
                <Form
                    form={editForm}
                    layout="vertical"
                    onFinish={handleEditWorkspace}
                >
                    <Form.Item
                        name="name"
                        label="Tên workspace"
                        rules={[
                            {
                                required: true,
                                message: "Vui lòng nhập tên workspace",
                            },
                            {
                                max: 50,
                                message:
                                    "Tên workspace không được vượt quá 50 ký tự",
                            },
                        ]}
                    >
                        <Input placeholder="Nhập tên workspace" />
                    </Form.Item>

                    <Form.Item
                        name="description"
                        label="Mô tả (tùy chọn)"
                        rules={[
                            {
                                max: 200,
                                message: "Mô tả không được vượt quá 200 ký tự",
                            },
                        ]}
                    >
                        <Input.TextArea
                            placeholder="Mô tả workspace"
                            rows={3}
                        />
                    </Form.Item>

                    <Form.Item className="modal-actions">
                        <Space>
                            <Button
                                onClick={() => {
                                    setIsEditModalVisible(false);
                                    setEditingWorkspace(null);
                                    editForm.resetFields();
                                }}
                            >
                                Hủy
                            </Button>
                            <Button type="primary" htmlType="submit">
                                Cập nhật
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default WorkspaceSelector;
