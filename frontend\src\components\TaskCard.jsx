import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import {
  Card,
  Avatar,
  Button,
  Dropdown,
  Checkbox,
  Badge,
  Popconfirm,
} from "antd";
import {
  UserOutlined,
  CalendarOutlined,
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  CaretDownOutlined,
  CaretRightOutlined,
  PaperClipOutlined,
  TagOutlined,
} from "@ant-design/icons";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import TaskEditModal from "./TaskEditModal";
import AssigneeSelector from "./AssigneeSelector";
import LabelBadge from "./LabelBadge";
import LabelSelector from "./LabelSelector";
import { formatDate } from "../utils/date";
import attachmentService from "../services/attachmentService";
import labelService from "../services/labelService";

const TaskCard = ({
  task,
  listId,
  boardId,
  workspaceId,
  onUpdateTask,
  onDeleteTask,
  onAddSubtask,
  onToggleTaskComplete,
  level = 0,
  isParent = false,
  isChildTask = false,
  childrenTasks = [],
  isExpanded = true,
  onToggleExpanded,
  onModalStateChange,
  boardMembers = null,
  searchText,
  setSearchResult,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAssigneePopoverOpen, setIsAssigneePopoverOpen] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [taskLabels, setTaskLabels] = useState([]);

  const {
    attributes,
    listeners,
    setNodeRef,
    transition,
    transform,
    isDragging,
  } = useSortable({
    id: task.id,
    disabled: isModalOpen,
    data: {
      type: "task",
      task: {
        ...task,
        children: childrenTasks,
        isParent,
        isChildTask,
      },
      listId,
    },
  });

  // Kiểm tra task đã hoàn thành chưa
  const isCompleted = task.status === "completed";

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "#ff4d4f";
      case "medium":
        return "#faad14";
      case "low":
        return "#52c41a";
      default:
        return "#d9d9d9";
    }
  };

  // Fetch task labels when component mounts
  useEffect(() => {
    const fetchTaskLabels = async () => {
      try {
        const labels = await labelService.getLabelsByTaskId(task.id);
        setTaskLabels(labels || []);
      } catch (error) {
        console.error("Error fetching task labels:", error);
      }
    };

    fetchTaskLabels();
  }, [task.id]);

  // Handle labels change
  const handleLabelsChange = (updatedLabels) => {
    setTaskLabels(updatedLabels);
  };

  const handleToggleComplete = async (e) => {
    e.stopPropagation();
    if (onToggleTaskComplete) {
      try {
        // Nếu đây là task cha và có children, toggle cả children
        if (!isChildTask && childrenTasks && childrenTasks.length > 0) {
          await onToggleTaskComplete(
            listId,
            task.id,
            !isCompleted,
            childrenTasks
          );
        } else {
          await onToggleTaskComplete(listId, task.id, !isCompleted);
        }
      } catch (error) {
        console.error("Error updating task status:", error);
      }
    }
  };

  // Menu items - task con không có option "Thêm task con"
  const taskMenuItems = [
    {
      key: "editTask",
      label: (
        <div className="dropdown-item">
          <EditOutlined />
          <span>Chỉnh sửa</span>
        </div>
      ),
      onClick: () => {
        setIsModalOpen(true);
        if (onModalStateChange) {
          onModalStateChange(true);
        }
      },
    },
    // Chỉ task cha mới có option "Thêm task con"
    ...(isChildTask
      ? []
      : [
          {
            key: "addSubtask",
            label: (
              <div className="dropdown-item">
                <PlusOutlined />
                <span>Thêm task con</span>
              </div>
            ),
            onClick: () => {
              handleAddSubtask();
            },
          },
        ]),
    {
      key: "delete",
      label: (
        <Popconfirm
          title={
            isParent && childrenTasks.length > 0
              ? "Xác nhận xóa task cha"
              : "Xác nhận xóa task"
          }
          description={
            isParent && childrenTasks.length > 0
              ? `Bạn có chắc chắn muốn xóa task này và ${childrenTasks.length} task con?`
              : "Bạn có chắc chắn muốn xóa?"
          }
          onConfirm={() => onDeleteTask(listId, task.id)}
          okText="Xóa"
          cancelText="Hủy"
          okType="danger"
          placement="bottom"
        >
          <div className="dropdown-item delete-item">
            <DeleteOutlined />
            <span>Xóa</span>
          </div>
        </Popconfirm>
      ),
    },
  ];

  const handleAddSubtask = () => {
    if (onAddSubtask) {
      onAddSubtask(listId, task.id);
    }
  };

  const handleCardDoubleClick = () => {
    // Don't open edit modal if assigning
    if (isAssigneePopoverOpen) {
      return;
    }
    setIsModalOpen(true);
    if (onModalStateChange) {
      onModalStateChange(true);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    if (onModalStateChange) {
      onModalStateChange(false);
    }
  };

  const style = {
    touchAction: "none",
    transition,
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.5 : undefined,
    cursor: isDragging ? "grabbing" : "grab",
  };

  // Styling cho task con (chỉ 1 cấp đơn giản)
  const leftMargin = isChildTask ? 40 : 0; // Tăng margin cho task con lùi vào trong hơn

  const cardStyle = {
    marginBottom: 8,
    marginLeft: leftMargin,
    position: "relative",
    ...(isChildTask && {
      border: "1px solid #e8e8e8",
      borderLeft: "4px solid #1890ff",
      backgroundColor: "#fafafa",
      borderRadius: "6px",
      boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
    }),
    ...(isCompleted && {
      backgroundColor: "#f5f5f5",
      opacity: 0.7,
    }),
  };

  return (
    <>
      <div ref={setNodeRef} style={style} {...attributes}>
        <Card
          className={`task-card ${isChildTask ? "subtask-card" : ""} ${
            isCompleted ? "completed-task" : ""
          } ${isParent ? "has-children" : ""} ${
            isParent && !isExpanded ? "collapsed" : ""
          }`}
          size="small"
          style={cardStyle}
          hoverable
          {...(!isModalOpen && listeners)}
          onDoubleClick={handleCardDoubleClick}
        >
          <div className="task-header">
            {/* Nút thu gọn/mở rộng cho task cha có children */}
            {isParent && childrenTasks.length > 0 && (
              <Button
                type="text"
                size="small"
                className="task-expand-btn"
                icon={
                  isExpanded ? <CaretDownOutlined /> : <CaretRightOutlined />
                }
                onClick={(e) => {
                  e.stopPropagation();
                  if (onToggleExpanded) {
                    onToggleExpanded();
                  }
                }}
                title={isExpanded ? "Thu gọn task con" : "Mở rộng task con"}
              />
            )}

            <Checkbox
              checked={isCompleted}
              onChange={handleToggleComplete}
              style={{ marginRight: "8px" }}
              onClick={(e) => e.stopPropagation()}
            />

            <div
              className="task-priority"
              style={{
                backgroundColor: getPriorityColor(task.priority),
              }}
            ></div>
            <h4
              className={`task-title ${isChildTask ? "subtask-title" : ""}`}
              style={{
                ...(isChildTask
                  ? { fontSize: "13px", fontWeight: "normal" }
                  : {}),
                ...(isCompleted && {
                  textDecoration: "line-through",
                  color: "#8c8c8c",
                }),
              }}
            >
              {task.title}
            </h4>
            <Dropdown
              menu={{ items: taskMenuItems }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                size="small"
                className="task-menu-btn"
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>

          {/* Labels section */}
          {taskLabels.length > 0 && (
            <div className="task-labels" style={{ margin: "8px 0" }}>
              <div style={{ display: "flex", flexWrap: "wrap", gap: "4px" }}>
                {taskLabels.map((label) => (
                  <LabelBadge
                    key={label.id}
                    label={label}
                    size="small"
                    style={{ margin: 0 }}
                  />
                ))}
                <LabelSelector
                  taskId={task.id}
                  boardId={boardId}
                  taskLabels={taskLabels}
                  onLabelsChange={handleLabelsChange}
                  trigger="click"
                  placement="bottomLeft"
                >
                  <Button
                    type="text"
                    icon={<TagOutlined />}
                    size="small"
                    style={{
                      color: "#8c8c8c",
                      padding: "0 4px",
                      height: "20px",
                      minWidth: "20px",
                    }}
                    title="Quản lý labels"
                  />
                </LabelSelector>
              </div>
            </div>
          )}

          {/* Show label selector even when no labels */}
          {taskLabels.length === 0 && (
            <div className="task-labels" style={{ margin: "8px 0" }}>
              <LabelSelector
                taskId={task.id}
                boardId={boardId}
                taskLabels={taskLabels}
                onLabelsChange={handleLabelsChange}
                trigger="click"
                placement="bottomLeft"
              >
                <Button
                  type="text"
                  icon={<TagOutlined />}
                  size="small"
                  style={{
                    color: "#bfbfbf",
                    padding: "0 4px",
                    height: "20px",
                    minWidth: "20px",
                  }}
                  title="Thêm labels"
                />
              </LabelSelector>
            </div>
          )}

          <div className="task-description">
            <p
              className="task-desc"
              style={{
                margin: "8px 0",
                fontSize: "13px",
                color: task.notes
                  ? isCompleted
                    ? "#a6a6a6"
                    : "#595959"
                  : "#bfbfbf",
                minHeight: "20px",
                maxHeight: "40px",
                fontStyle: task.notes ? "normal" : "italic",
                lineHeight: "1.4",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                wordBreak: "break-word",
                ...(isCompleted && {
                  textDecoration: "line-through",
                }),
              }}
              title={task.notes || "Không có mô tả..."}
            >
              {task.notes || "Không có mô tả..."}
            </p>
          </div>
          <div className="task-details">
            <div className="task-assignee">
              <AssigneeSelector
                task={task}
                listId={listId}
                boardId={boardId}
                workspaceId={workspaceId}
                onUpdateTask={onUpdateTask}
                isCompleted={isCompleted}
                size="small"
                boardMembers={boardMembers}
                onPopoverStateChange={setIsAssigneePopoverOpen}
              />
            </div>

            {attachments.length > 0 && (
              <div
                className="task-attachments"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                  marginLeft: "8px",
                }}
              >
                <PaperClipOutlined />
                <span>{attachments.length}</span>
              </div>
            )}
            <div className="task-due-date">
              <CalendarOutlined
                style={{
                  marginRight: "4px",
                  color: isCompleted ? "#8c8c8c" : undefined,
                }}
              />
              <span
                className="date-text"
                style={isCompleted ? { color: "#8c8c8c" } : {}}
              >
                {task.due ? formatDate(task.due) : ""}
              </span>
            </div>
          </div>
        </Card>
      </div>

      {isModalOpen && (
        <TaskEditModal
          visible={isModalOpen}
          task={task}
          listId={listId}
          boardId={boardId}
          workspaceId={workspaceId}
          onClose={() => setIsModalOpen(false)}
          onUpdateTask={onUpdateTask}
          isChildTask={isChildTask}
          searchText={searchText}
          setSearchResult={setSearchResult}
        />
      )}
    </>
  );
};

export default TaskCard;
