import { Empty } from "antd";
import ReadOnlyTaskCard from "./ReadOnlyTaskCard";
import "../pages/DashBoard.css";

const ReadOnlyTaskList = ({ taskList, onTaskDoubleClick }) => {
    // Organize tasks into parent-child structure
    const organizeTasksWithChildren = (tasks) => {
        const taskMap = new Map();
        const parentTasks = [];

        // First pass: create task map
        tasks.forEach((task) => {
            taskMap.set(task.id, { ...task, children: [] });
        });

        // Second pass: organize parent-child relationships
        tasks.forEach((task) => {
            if (task.parent) {
                const parent = taskMap.get(task.parent);
                if (parent) {
                    parent.children.push(taskMap.get(task.id));
                } else {
                    parentTasks.push(taskMap.get(task.id));
                }
            } else {
                parentTasks.push(taskMap.get(task.id));
            }
        });

        return parentTasks;
    };

    const organizedTasks = organizeTasksWithChildren(taskList.tasks || []);
    const completedCount = (taskList.tasks || []).filter(
        (task) => task.status === "completed"
    ).length;
    const totalCount = taskList.tasks?.length || 0;

    return (
        <div className="board-column read-only">
            <div className="column-header">
                <div className="column-title">
                    <div
                        className="list-color"
                        style={{
                            backgroundColor: taskList.color || "#1890FF",
                            width: "12px",
                            height: "12px",
                            borderRadius: "50%",
                            marginRight: "8px",
                        }}
                    ></div>
                    <h3 className="list-name">
                        {taskList.title || taskList.name}
                    </h3>
                </div>
            </div>

            <div className="column-content">
                {/* Task List Description */}
                {taskList.description && (
                    <div
                        style={{
                            padding: "12px 0",
                            borderBottom: "1px solid #f0f0f0",
                            marginBottom: "16px",
                        }}
                    >
                        <div
                            style={{
                                fontSize: "13px",
                                color: "#666",
                                lineHeight: "1.4",
                                maxHeight: "calc(1.4em * 3)", // Giới hạn 3 dòng
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                display: "-webkit-box",
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: "vertical",
                            }}
                        >
                            {taskList.description}
                        </div>
                    </div>
                )}

                {/* Progress indicator */}
                <div
                    style={{
                        padding: "12px 0",
                        borderBottom: "1px solid #f0f0f0",
                        marginBottom: "16px",
                    }}
                >
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            fontSize: "12px",
                            color: "#8c8c8c",
                        }}
                    >
                        <span>Tiến độ</span>
                        <span>
                            {completedCount}/{totalCount}
                        </span>
                    </div>
                    <div
                        style={{
                            width: "100%",
                            height: "4px",
                            background: "#f0f0f0",
                            borderRadius: "2px",
                            marginTop: "4px",
                            overflow: "hidden",
                        }}
                    >
                        <div
                            style={{
                                width: `${
                                    totalCount > 0
                                        ? (completedCount / totalCount) * 100
                                        : 0
                                }%`,
                                height: "100%",
                                background:
                                    completedCount === totalCount &&
                                    totalCount > 0
                                        ? "#52c41a"
                                        : "#1890ff",
                                borderRadius: "2px",
                                transition: "width 0.3s ease",
                            }}
                        ></div>
                    </div>
                </div>

                {organizedTasks.length > 0 ? (
                    <div className="tasks-container">
                        {organizedTasks.map((task) => (
                            <ReadOnlyTaskCard
                                key={task.id}
                                task={task}
                                isParent={
                                    task.children && task.children.length > 0
                                }
                                childrenTasks={task.children || []}
                                onTaskDoubleClick={onTaskDoubleClick}
                            />
                        ))}
                    </div>
                ) : (
                    <Empty
                        description="Chưa có task nào"
                        style={{
                            margin: "16px 0",
                            color: "#8c8c8c",
                        }}
                    />
                )}
            </div>
        </div>
    );
};

export default ReadOnlyTaskList;
