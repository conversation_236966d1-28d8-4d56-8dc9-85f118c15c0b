import React from "react";
import { Modal, Typography, Space, Avatar, Tag, Divider } from "antd";
import {
    CalendarOutlined,
    UserOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    FlagOutlined,
    FileTextOutlined,
    InfoCircleOutlined,
} from "@ant-design/icons";
import { formatDate } from "../utils/date";

const { Title, Text, Paragraph } = Typography;

const TaskDetailModal = ({ task, visible, onClose }) => {
    if (!task) return null;

    const isCompleted = task.status === "completed";

    const getPriorityColor = (priority) => {
        switch (priority) {
            case "high":
                return "#ff4d4f";
            case "medium":
                return "#faad14";
            case "low":
                return "#52c41a";
            default:
                return "#d9d9d9";
        }
    };

    const getPriorityText = (priority) => {
        switch (priority) {
            case "high":
                return "Cao";
            case "medium":
                return "Trung bình";
            case "low":
                return "Thấp";
            default:
                return "Không xác định";
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case "completed":
                return "Hoàn thành";
            case "needsAction":
                return "Cần thực hiện";
            default:
                return "Không xác định";
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case "completed":
                return "#52c41a";
            case "needsAction":
                return "#faad14";
            default:
                return "#d9d9d9";
        }
    };

    return (
        <Modal
            title={
                <Space align="center">
                    <InfoCircleOutlined style={{ color: "#1890ff" }} />
                    <span>Chi tiết Task</span>
                </Space>
            }
            open={visible}
            onCancel={onClose}
            footer={null}
            width={600}
            style={{ top: 20 }}
        >
            <div style={{ padding: "16px 0" }}>
                {/* Task Title */}
                <div style={{ marginBottom: "24px" }}>
                    <Title
                        level={4}
                        style={{
                            margin: 0,
                            textDecoration: isCompleted
                                ? "line-through"
                                : "none",
                            color: isCompleted ? "#8c8c8c" : "inherit",
                        }}
                    >
                        {task.title}
                    </Title>
                </div>

                {/* Status and Priority */}
                <div style={{ marginBottom: "16px" }}>
                    <Space size="large">
                        <div>
                            <Text strong style={{ marginRight: "8px" }}>
                                Trạng thái:
                            </Text>
                            <Tag
                                icon={
                                    isCompleted ? (
                                        <CheckCircleOutlined />
                                    ) : (
                                        <ClockCircleOutlined />
                                    )
                                }
                                color={getStatusColor(task.status)}
                            >
                                {getStatusText(task.status)}
                            </Tag>
                        </div>

                        {task.priority && task.priority !== "none" && (
                            <div>
                                <Text strong style={{ marginRight: "8px" }}>
                                    Độ ưu tiên:
                                </Text>
                                <Tag color={getPriorityColor(task.priority)}>
                                    {getPriorityText(task.priority)}
                                </Tag>
                            </div>
                        )}
                    </Space>
                </div>

                <Divider />

                {/* Task Notes */}
                {task.notes && (
                    <div style={{ marginBottom: "16px" }}>
                        <Text
                            strong
                            style={{ display: "block", marginBottom: "8px" }}
                        >
                            <FileTextOutlined style={{ marginRight: "8px" }} />
                            Ghi chú:
                        </Text>
                        <Paragraph
                            className="task-notes-content"
                            style={{
                                background: "#fafafa",
                                padding: "12px",
                                borderRadius: "6px",
                                margin: 0,
                                whiteSpace: "pre-wrap",
                                maxHeight: "calc(1.5em * 15)", // 15 dòng (1.5em per line)
                                overflowY: "auto",
                                lineHeight: "1.5",
                                wordBreak: "break-word",
                            }}
                        >
                            {task.notes}
                        </Paragraph>
                    </div>
                )}

                {/* Due Date */}
                {task.due && (
                    <div style={{ marginBottom: "16px" }}>
                        <Text strong style={{ marginRight: "8px" }}>
                            <CalendarOutlined style={{ marginRight: "8px" }} />
                            Hạn hoàn thành:
                        </Text>
                        <Text>{formatDate(task.due)}</Text>
                    </div>
                )}

                {/* Assignee */}
                {task.assignee && (
                    <div style={{ marginBottom: "16px" }}>
                        <Text
                            strong
                            style={{ display: "block", marginBottom: "8px" }}
                        >
                            <UserOutlined style={{ marginRight: "8px" }} />
                            Người được giao:
                        </Text>
                        <Space>
                            <Avatar
                                src={task.assignee.photoUrl}
                                icon={<UserOutlined />}
                                size="small"
                            />
                            <Text>
                                {task.assignee.fullName || task.assignee.email}
                            </Text>
                        </Space>
                    </div>
                )}
            </div>
        </Modal>
    );
};

export default TaskDetailModal;
